'use client';

import { useState, useRef, useEffect } from 'react';
import type { ChatMessage } from '@/lib/types';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Send, Mic, MicOff } from 'lucide-react';
import { cn } from '@/lib/utils';
import { getStt } from '@/utils/audio';
import { useSttSupport } from '@/hooks/use-client-only';
import { useToast } from '@/hooks/use-toast';

interface ChatBoxProps {
    messages: ChatMessage[];
    onSendMessage: (message: string) => void;
    disabled: boolean;
    isProcessing: boolean;
}

export function ChatBox({ messages, onSendMessage, disabled, isProcessing }: ChatBoxProps) {
    const [input, setInput] = useState('');
    const [isListening, setIsListening] = useState(false);
    const messagesEndRef = useRef<HTMLDivElement>(null);
    const { isClient, isSupported: sttSupported, isReady } = useSttSupport();
    const { toast } = useToast();

    const scrollToBottom = () => {
        messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
    };

    useEffect(() => {
        scrollToBottom();
    }, [messages]);

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        if (input.trim() && !disabled && !isProcessing) {
            onSendMessage(input.trim());
            setInput('');
        }
    };

    const handleMicClick = () => {
        const stt = getStt();
        if (!stt || !sttSupported) {
            toast({
                title: "Speech Recognition Not Available",
                description: "Your browser doesn't support speech recognition.",
                variant: "destructive"
            });
            return;
        }

        if (isListening) {
            // Stop listening
            stt.stopListening();
            setIsListening(false);
        } else {
            // Start listening
            stt.startListening({
                onStart: () => {
                    setIsListening(true);
                    toast({
                        title: "🎤 Listening...",
                        description: "Speak your command now",
                        variant: "default"
                    });
                },
                onResult: (text) => {
                    setInput(text);
                    setIsListening(false);
                    toast({
                        title: "✅ Speech Recognized",
                        description: `"${text}"`,
                        variant: "default"
                    });
                },
                onError: (error) => {
                    setIsListening(false);
                    toast({
                        title: "🎤 Speech Recognition Error",
                        description: error,
                        variant: "destructive"
                    });
                },
                onEnd: () => {
                    setIsListening(false);
                }
            });
        }
    };

    return (
        <div className="space-y-3 p-3 bg-slate-800/40 border border-green-500/30 rounded-lg">
            <div className="flex items-center gap-2">
                <h3 className="font-mono text-sm text-green-300">◢ NEURAL_LINK ◣</h3>
                {isListening && (
                    <div className="flex items-center gap-1">
                        <Mic className="h-3 w-3 text-red-400 animate-pulse" />
                        <span className="text-xs text-red-400 font-mono">LISTENING</span>
                    </div>
                )}
                <div className="flex-1 h-0.5 bg-gradient-to-r from-green-500/50 to-transparent"></div>
                <div className={cn(
                    "w-2 h-2 rounded-full animate-pulse",
                    isListening ? "bg-red-400" : isProcessing ? "bg-yellow-400" : "bg-green-400"
                )}></div>
            </div>

            <ScrollArea className="h-40 w-full rounded-md border border-green-500/20 bg-slate-900/50 p-3">
                <div className="space-y-2">
                    {messages.map((msg, index) => (
                        <div
                            key={index}
                            className={cn(
                                "flex items-start gap-2 text-sm",
                                msg.sender === 'user' ? 'justify-end' : 'justify-start'
                            )}
                        >
                            <p className={cn(
                                "max-w-[85%] rounded-lg px-3 py-2 font-mono text-xs",
                                msg.sender === 'user'
                                    ? 'bg-gradient-to-br from-cyan-600/80 to-blue-600/80 text-cyan-100 border border-cyan-400/30'
                                    : 'bg-gradient-to-br from-green-600/80 to-emerald-600/80 text-green-100 border border-green-400/30'
                            )}>
                                {msg.sender === 'user' ? `> ${msg.message}` : `AI: ${msg.message}`}
                            </p>
                        </div>
                    ))}
                    <div ref={messagesEndRef} />
                 </div>
            </ScrollArea>

            <form onSubmit={handleSubmit} className="flex gap-2">
                <Input
                    value={input}
                    onChange={(e) => setInput(e.target.value)}
                    placeholder={isListening ? "🎤 Listening..." : ">> enter_command..."}
                    disabled={disabled || isProcessing || isListening}
                    className={cn(
                        "bg-slate-700/50 border-green-500/30 text-green-200 placeholder:text-green-400/50 font-mono text-sm",
                        isListening && "border-red-400/50 bg-red-900/20"
                    )}
                />

                {/* Microphone Button */}
                {isReady && sttSupported && (
                    <Button
                        type="button"
                        onClick={handleMicClick}
                        disabled={disabled || isProcessing}
                        size="icon"
                        title={isListening ? "Click to stop listening" : "Click to speak your command"}
                        className={cn(
                            "border shadow-[0_0_10px_rgba(34,197,94,0.3)]",
                            isListening
                                ? "bg-gradient-to-br from-red-600/80 to-orange-600/80 hover:from-red-500 hover:to-orange-500 border-red-400/50 animate-pulse"
                                : "bg-gradient-to-br from-purple-600/80 to-pink-600/80 hover:from-purple-500 hover:to-pink-500 border-purple-400/50"
                        )}
                    >
                        {isListening ? (
                            <MicOff className="h-4 w-4 text-red-100" />
                        ) : (
                            <Mic className="h-4 w-4 text-purple-100" />
                        )}
                    </Button>
                )}

                {/* Send Button */}
                <Button
                    type="submit"
                    disabled={disabled || isProcessing || isListening}
                    size="icon"
                    className="bg-gradient-to-br from-green-600/80 to-emerald-600/80 hover:from-green-500 hover:to-emerald-500 border border-green-400/50 shadow-[0_0_10px_rgba(34,197,94,0.3)]"
                >
                    {isProcessing ? (
                        <div className="w-4 h-4 border-2 border-green-300 border-t-transparent rounded-full animate-spin"></div>
                    ) : (
                        <Send className="h-4 w-4 text-green-100" />
                    )}
                </Button>
            </form>

            {/* Help text for speech-to-text */}
            {isReady && sttSupported && (
                <div className="text-xs text-green-400/60 font-mono text-center">
                    💡 Click 🎤 to speak commands or type them manually
                </div>
            )}
        </div>
    );
}
