'use client';

import { useState, useEffect } from 'react';
import type { Direction, ChatMessage } from '@/lib/types';

// Removed laser suggestion functionality - now using tool-based mission system
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ArrowUp, ArrowDown, ArrowLeft, ArrowRight, Zap, RefreshCw, Bot, AlertTriangle, Volume2, VolumeX } from 'lucide-react';
import { getTts } from '@/utils/audio';
import { useTtsSupport } from '@/hooks/use-client-only';
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { ChatBox } from './chat-box';
import { ScrollArea } from '../ui/scroll-area';
import {
  Dialog,
  DialogContent,
  DialogDes<PERSON>,
  Di<PERSON><PERSON>eader,
  Di<PERSON><PERSON>it<PERSON>,
  DialogTrigger,
} from "@/components/ui/dialog";

interface GameStatsProps {
  score: number;
  batteries: number;
  onMove: (direction: Direction) => void;
  onFire: (direction: Direction) => void;
  onReset: () => void;
  gameOver: boolean;
  chatHistory: ChatMessage[];
  onChatSubmit: (message: string) => void;
  isChatProcessing: boolean;
  commandLogs: string[];
  isBotOnMission: boolean;
  mission: string | null;
  lastCommandDebugInfo: object | null;
  workflowLog: Array<{
    timestamp: string;
    toolName: string;
    args: any;
    result: any;
    reasoning: string;
  }>;
  showWorkflowLog: boolean;
  onToggleWorkflowLog: () => void;
}

export function GameStats({
  score,
  batteries,
  onMove,
  onFire,
  onReset,
  gameOver,
  chatHistory,
  onChatSubmit,
  isChatProcessing,
  commandLogs,
  isBotOnMission,
  mission,
  lastCommandDebugInfo,
  workflowLog,
  showWorkflowLog,
  onToggleWorkflowLog,
}: GameStatsProps) {
  const [laserDirection, setLaserDirection] = useState<Direction>('up');
  const [isTtsEnabled, setIsTtsEnabled] = useState(true);
  const [availableVoices, setAvailableVoices] = useState<SpeechSynthesisVoice[]>([]);
  const [selectedVoice, setSelectedVoice] = useState<string>('');
  const [hasPlayedWelcome, setHasPlayedWelcome] = useState(false);
  const { isClient, isSupported: ttsSupported, isReady } = useTtsSupport();
  const isBusy = gameOver || isBotOnMission;

  // Load available voices when component mounts
  useEffect(() => {
    if (isReady && ttsSupported) {
      const loadVoices = () => {
        const tts = getTts();
        if (tts) {
          const voices = tts.getFilteredVoices();
          console.log('🔊 Loading voices for UI:', voices.length);
          setAvailableVoices(voices);

          const currentVoice = tts.getCurrentVoice();
          if (currentVoice) {
            setSelectedVoice(currentVoice.name);
            console.log('🔊 Current voice set to:', currentVoice.name);
          }
        }
      };

      // Try loading voices immediately
      loadVoices();

      // Also try after a delay in case voices aren't loaded yet
      setTimeout(loadVoices, 1000);
      setTimeout(loadVoices, 3000);
    }
  }, [isReady, ttsSupported]);

  const toggleTts = () => {
    const tts = getTts();
    if (!tts) return;

    const newState = !isTtsEnabled;
    setIsTtsEnabled(newState);
    tts.setEnabled(newState);

    if (newState) {
      // Delay slightly to ensure TTS is ready
      setTimeout(() => {
        if (!hasPlayedWelcome) {
          // Play welcome message on first TTS enable
          const welcomeMessage = "Greetings! I am Robert, your AI companion. I have an unusual obsession with collecting batteries - they're simply magnificent! However, I must confess a deep hatred for turtles due to a childhood trauma involving Ninja Turtles and way too much pizza. Voice systems are now online!";
          tts.speakAsRobert(welcomeMessage, { interrupt: true });
          setHasPlayedWelcome(true);
        } else {
          // Just announce TTS is enabled
          tts.speakAsRobert("Robert's voice systems online. Audio enabled.", { interrupt: true });
        }
      }, 100);
    }
  };

  const handleVoiceChange = (voiceName: string) => {
    const tts = getTts();
    if (!tts) return;

    if (tts.setVoiceByName(voiceName)) {
      setSelectedVoice(voiceName);
      if (isTtsEnabled) {
        // Delay voice change announcement to avoid interrupting other speech
        setTimeout(() => {
          tts.speakAsRobert("Voice changed. This is Robert speaking with the new voice.", { interrupt: false });
        }, 500);
      }
    }
  };

  const testVoice = () => {
    const tts = getTts();
    if (!tts || !isTtsEnabled) return;

    tts.speakAsRobert("Greetings! I am Robert. I love batteries and hate turtles!", { interrupt: true });
  };

  const speakWelcome = () => {
    const tts = getTts();
    if (!tts || !isTtsEnabled) return;

    const welcomeMessage = "Greetings! I am Robert, your AI companion. I have an unusual obsession with collecting batteries - they're simply magnificent! However, I must confess a deep hatred for turtles due to a childhood trauma involving Ninja Turtles and way too much pizza. Now, how may I assist you in this grid-based adventure?";
    tts.speakAsRobert(welcomeMessage, { interrupt: true });
  };

  const debugVoices = () => {
    const tts = getTts();
    if (!tts) {
      console.log('🔊 TTS not available');
      return;
    }

    console.log('🔊 Current voice:', tts.getCurrentVoiceInfo());
    tts.logAvailableVoices();

    const voices = tts.getFilteredVoices();
    console.log('🔊 Filtered voices for UI:', voices.map(v => `${v.name} (${v.lang})`));
  };

  return (
    <Card className="flex flex-col bg-slate-900/90 border-cyan-500/30 shadow-[0_0_20px_rgba(34,211,238,0.2)] text-cyan-100">
      <CardHeader className="bg-gradient-to-r from-slate-800/50 to-slate-700/50 border-b border-cyan-500/20">
        <CardTitle className="font-mono text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-purple-400">
          ◢ NEURAL INTERFACE ◣
        </CardTitle>
        <CardDescription className="text-cyan-300/70 font-mono text-xs">
          SYS_STATUS: ONLINE | GRID_ACTIVE | AI_ENABLED
        </CardDescription>
      </CardHeader>
      <CardContent className="flex flex-col gap-4 bg-slate-800/30">
        <div className="flex justify-around text-center">
          <div className="relative p-3 bg-gradient-to-br from-cyan-500/10 to-blue-500/10 border border-cyan-500/30 rounded-lg">
            <div className="absolute top-1 right-1 w-1 h-1 bg-cyan-400 rounded-full animate-pulse"></div>
            <p className="text-2xl font-bold font-mono text-cyan-300">{score}</p>
            <p className="text-xs text-cyan-400/70 font-mono">SCORE_VAL</p>
          </div>
          <div className="relative p-3 bg-gradient-to-br from-yellow-500/10 to-amber-500/10 border border-yellow-500/30 rounded-lg">
            <div className="absolute top-1 right-1 w-1 h-1 bg-yellow-400 rounded-full animate-pulse"></div>
            <p className="text-2xl font-bold font-mono text-yellow-300">{batteries} ⚡</p>
            <p className="text-xs text-yellow-400/70 font-mono">PWR_CELLS</p>
          </div>
        </div>
        
        {gameOver && (
            <Alert variant="destructive" className="bg-red-900/20 border-red-500/50 text-red-300">
                <AlertTriangle className="h-4 w-4 text-red-400" />
                <AlertTitle className="font-mono">SYSTEM_FAILURE</AlertTitle>
                <AlertDescription className="font-mono text-xs">
                    NEURAL_LINK_SEVERED // Reset required to restore connection
                </AlertDescription>
            </Alert>
        )}

        {isBotOnMission && mission && (
            <Alert className="bg-cyan-900/20 border-cyan-500/50 text-cyan-300">
                <Bot className="h-4 w-4 animate-pulse text-cyan-400" />
                <AlertTitle className="font-mono">MISSION_ACTIVE</AlertTitle>
                <AlertDescription className="font-mono text-xs">
                    OBJECTIVE: {mission.toUpperCase().replace('_', '_')}
                </AlertDescription>
            </Alert>
        )}

        <Separator />
        
        <ChatBox 
            messages={chatHistory} 
            onSendMessage={onChatSubmit}
            disabled={isBusy}
            isProcessing={isChatProcessing}
        />
        
        <Separator />

        <div className="space-y-3 p-3 bg-slate-800/40 border border-purple-500/30 rounded-lg">
            <h3 className="font-mono text-sm text-purple-300">◢ MOVEMENT_CTRL ◣</h3>
            <div className="grid grid-cols-3 gap-2 justify-items-center">
                <div></div>
                <Button
                    aria-label="Move Up"
                    size="icon"
                    onClick={() => onMove('up')}
                    disabled={isBusy}
                    className="bg-gradient-to-br from-cyan-600/80 to-blue-600/80 hover:from-cyan-500 hover:to-blue-500 border border-cyan-400/50 shadow-[0_0_10px_rgba(34,211,238,0.3)]"
                >
                    <ArrowUp className="text-cyan-100" />
                </Button>
                <div></div>
                <Button
                    aria-label="Move Left"
                    size="icon"
                    onClick={() => onMove('left')}
                    disabled={isBusy}
                    className="bg-gradient-to-br from-cyan-600/80 to-blue-600/80 hover:from-cyan-500 hover:to-blue-500 border border-cyan-400/50 shadow-[0_0_10px_rgba(34,211,238,0.3)]"
                >
                    <ArrowLeft className="text-cyan-100" />
                </Button>
                <Button
                    aria-label="Move Down"
                    size="icon"
                    onClick={() => onMove('down')}
                    disabled={isBusy}
                    className="bg-gradient-to-br from-cyan-600/80 to-blue-600/80 hover:from-cyan-500 hover:to-blue-500 border border-cyan-400/50 shadow-[0_0_10px_rgba(34,211,238,0.3)]"
                >
                    <ArrowDown className="text-cyan-100" />
                </Button>
                <Button
                    aria-label="Move Right"
                    size="icon"
                    onClick={() => onMove('right')}
                    disabled={isBusy}
                    className="bg-gradient-to-br from-cyan-600/80 to-blue-600/80 hover:from-cyan-500 hover:to-blue-500 border border-cyan-400/50 shadow-[0_0_10px_rgba(34,211,238,0.3)]"
                >
                    <ArrowRight className="text-cyan-100" />
                </Button>
            </div>
        </div>
        
        <Separator />

        <div className="space-y-3 p-3 bg-slate-800/40 border border-red-500/30 rounded-lg">
            <h3 className="font-mono text-sm text-red-300">◢ WEAPON_SYS ◣</h3>
            <div className="flex gap-2">
                <Select
                    defaultValue="up"
                    onValueChange={(v) => setLaserDirection(v as Direction)}
                    disabled={isBusy}
                >
                    <SelectTrigger className="bg-slate-700/50 border-red-500/30 text-red-200 font-mono">
                        <SelectValue placeholder="TARGET_DIR" />
                    </SelectTrigger>
                    <SelectContent className="bg-slate-800 border-red-500/30">
                        <SelectItem value="up" className="text-red-200 font-mono">↑ NORTH</SelectItem>
                        <SelectItem value="down" className="text-red-200 font-mono">↓ SOUTH</SelectItem>
                        <SelectItem value="left" className="text-red-200 font-mono">← WEST</SelectItem>
                        <SelectItem value="right" className="text-red-200 font-mono">→ EAST</SelectItem>
                    </SelectContent>
                </Select>
                <Button
                    onClick={() => onFire(laserDirection)}
                    disabled={isBusy || batteries <= 0}
                    className="flex-grow bg-gradient-to-br from-red-600/80 to-orange-600/80 hover:from-red-500 hover:to-orange-500 border border-red-400/50 shadow-[0_0_15px_rgba(239,68,68,0.4)] font-mono"
                >
                    <Zap className="mr-2 h-4 w-4" /> FIRE_LASER
                </Button>
            </div>
        </div>

        <Separator />
        
        {/* Removed AI Mode section - now always using deterministic approach */}
        
        {commandLogs.length > 0 && (
            <>
                <Separator />
                <div className="space-y-2">
                    <h3 className="font-semibold font-headline">AI Reasoning Log</h3>
                    <ScrollArea className="h-24 w-full rounded-md border p-2">
                        <div className="space-y-2">
                            {commandLogs.map((log, index) => (
                                <Alert key={index}>
                                    <Bot className="h-4 w-4" />
                                    <AlertDescription>
                                        {log}
                                    </AlertDescription>
                                </Alert>
                            ))}
                        </div>
                    </ScrollArea>
                </div>
            </>
        )}

        {workflowLog.length > 0 && (
            <>
                <Separator />
                <div className="space-y-2">
                    <div className="flex items-center justify-between">
                        <h3 className="font-semibold font-headline">🔧 Workflow Log</h3>
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={onToggleWorkflowLog}
                        >
                            {showWorkflowLog ? 'Hide' : 'Show'} ({workflowLog.length})
                        </Button>
                    </div>

                    {showWorkflowLog && (
                        <ScrollArea className="h-48 w-full rounded-md border p-2">
                            <div className="space-y-2">
                                {workflowLog.map((step, index) => (
                                    <Alert key={index} className="text-xs">
                                        <Bot className="h-3 w-3" />
                                        <AlertTitle className="text-xs font-mono">
                                            🔧 {step.toolName}
                                        </AlertTitle>
                                        <AlertDescription className="text-xs space-y-1">
                                            <div><strong>Args:</strong> {JSON.stringify(step.args)}</div>
                                            <div><strong>Result:</strong> {JSON.stringify(step.result)}</div>
                                            <div><strong>Reasoning:</strong> {step.reasoning}</div>
                                            <div className="text-muted-foreground">
                                                {new Date(step.timestamp).toLocaleTimeString()}
                                            </div>
                                        </AlertDescription>
                                    </Alert>
                                ))}
                            </div>
                        </ScrollArea>
                    )}
                </div>
            </>
        )}

        <Separator />

        {/* Removed test screens section */}

        {/* Audio Controls */}
        <div className="space-y-2 p-3 bg-slate-800/40 border border-purple-500/30 rounded-lg">
          <h3 className="font-mono text-sm text-purple-300">◢ AUDIO_SYS ◣</h3>

          {/* TTS Toggle */}
          <div className="flex gap-2">
            <Button
              onClick={toggleTts}
              variant="outline"
              size="sm"
              disabled={!isReady || !ttsSupported}
              className={`flex-1 font-mono ${
                !isReady || !ttsSupported
                  ? 'bg-gradient-to-br from-gray-600/20 to-gray-700/20 border-gray-400/50 text-gray-400'
                  : isTtsEnabled
                    ? 'bg-gradient-to-br from-green-600/20 to-cyan-600/20 border-green-400/50 text-green-300'
                    : 'bg-gradient-to-br from-red-600/20 to-orange-600/20 border-red-400/50 text-red-300'
              }`}
            >
              {!isReady ? <VolumeX className="mr-2 h-4 w-4" /> :
               !ttsSupported ? <VolumeX className="mr-2 h-4 w-4" /> :
               isTtsEnabled ? <Volume2 className="mr-2 h-4 w-4" /> : <VolumeX className="mr-2 h-4 w-4" />}
              TTS_{!isReady ? 'LOADING' : !ttsSupported ? 'N/A' : isTtsEnabled ? 'ON' : 'OFF'}
            </Button>
          </div>

          {/* Voice Selection */}
          {isReady && ttsSupported && availableVoices.length > 0 && (
            <div className="space-y-2">
              <label className="text-xs text-purple-300/70 font-mono">ROBERT_VOICE:</label>
              <Select
                value={selectedVoice}
                onValueChange={handleVoiceChange}
                disabled={!isTtsEnabled}
              >
                <SelectTrigger className="bg-slate-700/50 border-purple-500/30 text-purple-200 font-mono text-xs">
                  <SelectValue placeholder="Select voice..." />
                </SelectTrigger>
                <SelectContent className="bg-slate-800 border-purple-500/30 max-h-48">
                  {availableVoices.map((voice) => (
                    <SelectItem
                      key={voice.name}
                      value={voice.name}
                      className="text-purple-200 font-mono text-xs hover:bg-purple-600/20"
                    >
                      {voice.name} ({voice.lang})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              {/* Voice Test Buttons */}
              <div className="grid grid-cols-2 gap-1">
                <Button
                  onClick={testVoice}
                  variant="outline"
                  size="sm"
                  disabled={!isTtsEnabled}
                  className="font-mono text-xs bg-gradient-to-br from-purple-600/20 to-pink-600/20 border-purple-400/50 text-purple-200 hover:from-purple-500/30 hover:to-pink-500/30"
                >
                  🔊 TEST
                </Button>
                <Button
                  onClick={speakWelcome}
                  variant="outline"
                  size="sm"
                  disabled={!isTtsEnabled}
                  className="font-mono text-xs bg-gradient-to-br from-cyan-600/20 to-blue-600/20 border-cyan-400/50 text-cyan-200 hover:from-cyan-500/30 hover:to-blue-500/30"
                >
                  👋 WELCOME
                </Button>
                <Button
                  onClick={debugVoices}
                  variant="outline"
                  size="sm"
                  className="col-span-2 font-mono text-xs bg-gradient-to-br from-yellow-600/20 to-orange-600/20 border-yellow-400/50 text-yellow-200 hover:from-yellow-500/30 hover:to-orange-500/30"
                >
                  🔍 DEBUG_VOICES
                </Button>
              </div>
            </div>
          )}
        </div>

        {lastCommandDebugInfo ? (
          <div className="flex w-full gap-2">
            <Button onClick={onReset} variant="outline" className="flex-1">
              <RefreshCw className="mr-2 h-4 w-4" />
              Reset Game
            </Button>
            <Dialog>
              <DialogTrigger asChild>
                <Button variant="secondary" className="flex-1">
                  <Bot className="mr-2 h-4 w-4" />
                  View Log
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-[80vw] md:max-w-[60vw]">
                <DialogHeader>
                  <DialogTitle>AI Debug Log</DialogTitle>
                  <DialogDescription>
                    The raw input and output for the last AI command.
                  </DialogDescription>
                </DialogHeader>
                <ScrollArea className="h-[70vh] rounded-md border">
                  <pre className="p-4 text-xs">
                    {JSON.stringify(lastCommandDebugInfo, null, 2)}
                  </pre>
                </ScrollArea>
              </DialogContent>
            </Dialog>
          </div>
        ) : (
          <Button onClick={onReset} variant="outline" className="w-full">
            <RefreshCw className="mr-2 h-4 w-4" />
            Reset Game
          </Button>
        )}
      </CardContent>
    </Card>
  );
}
