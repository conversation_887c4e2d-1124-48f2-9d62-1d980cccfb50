import { useEffect, useState } from 'react';

/**
 * Hook to ensure consistent SSR/client rendering
 * Returns false during SSR and initial render, true after hydration
 */
export function useClientOnly(): boolean {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  return isClient;
}

/**
 * Hook specifically for TTS functionality
 * Handles both client-side detection and TTS support
 */
export function useTtsSupport(): {
  isClient: boolean;
  isSupported: boolean;
  isReady: boolean;
} {
  const [isClient, setIsClient] = useState(false);
  const [isSupported, setIsSupported] = useState(false);

  useEffect(() => {
    setIsClient(true);

    // Check TTS support after hydration
    const checkSupport = () => {
      if (typeof window !== 'undefined' && 'speechSynthesis' in window) {
        setIsSupported(true);
      }
    };

    checkSupport();
  }, []);

  return {
    isClient,
    isSupported,
    isReady: isClient // Ready when client-side
  };
}

/**
 * Hook specifically for STT (Speech-to-Text) functionality
 * Handles both client-side detection and STT support
 */
export function useSttSupport(): {
  isClient: boolean;
  isSupported: boolean;
  isReady: boolean;
} {
  const [isClient, setIsClient] = useState(false);
  const [isSupported, setIsSupported] = useState(false);

  useEffect(() => {
    setIsClient(true);

    // Check STT support after hydration
    const checkSupport = () => {
      if (typeof window !== 'undefined' &&
          ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window)) {
        setIsSupported(true);
      }
    };

    checkSupport();
  }, []);

  return {
    isClient,
    isSupported,
    isReady: isClient // Ready when client-side
  };
}
