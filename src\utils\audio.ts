/**
 * Audio utilities for the game
 */

// Check if we're in a browser environment
const isBrowser = typeof window !== 'undefined';

// Text-to-Speech functionality using Web Speech API
export class TextToSpeech {
  private static instance: TextToSpeech | null = null;
  private synth: SpeechSynthesis | null = null;
  private isEnabled: boolean = true;
  private voice: SpeechSynthesisVoice | null = null;

  private constructor() {
    if (isBrowser && 'speechSynthesis' in window) {
      this.synth = window.speechSynthesis;
      this.initializeVoice();
    }
  }

  public static getInstance(): TextToSpeech {
    if (!TextToSpeech.instance) {
      TextToSpeech.instance = new TextToSpeech();
    }
    return TextToSpeech.instance;
  }

  private initializeVoice() {
    if (!this.synth) return;

    // Wait for voices to be loaded
    const setVoice = () => {
      if (!this.synth) return;

      const voices = this.synth.getVoices();

      // Prefer male/robotic English voices, with fallback options
      const preferredVoices = [
        'Microsoft David - English (United States)', // Male voice
        'Google UK English Male',
        'Google US English Male',
        '<PERSON>', // macOS male voice
        '<PERSON>', // macOS male voice
        '<PERSON>', // macOS novelty voice (robotic)
        'Ralph', // macOS novelty voice
        'Microsoft Mark - English (United States)', // Male voice
        'Google US English',
        'Microsoft Zira - English (United States)',
        'Samantha', // macOS female fallback
      ];

      for (const preferredName of preferredVoices) {
        const voice = voices.find(v => v.name.includes(preferredName));
        if (voice) {
          this.voice = voice;
          console.log('🔊 TTS Voice selected:', voice.name);
          return;
        }
      }

      // Try to find male English voices
      const maleVoices = voices.filter(v =>
        v.lang.startsWith('en') &&
        (v.name.toLowerCase().includes('male') ||
         v.name.toLowerCase().includes('david') ||
         v.name.toLowerCase().includes('mark') ||
         v.name.toLowerCase().includes('alex') ||
         v.name.toLowerCase().includes('daniel') ||
         v.name.toLowerCase().includes('fred') ||
         v.name.toLowerCase().includes('ralph'))
      );

      if (maleVoices.length > 0) {
        this.voice = maleVoices[0];
        console.log('🔊 TTS Male voice selected:', maleVoices[0].name);
      } else {
        // Fallback to any English voice
        const englishVoice = voices.find(v => v.lang.startsWith('en'));
        if (englishVoice) {
          this.voice = englishVoice;
          console.log('🔊 TTS Fallback voice selected:', englishVoice.name);
        } else if (voices.length > 0) {
          this.voice = voices[0];
          console.log('🔊 TTS Default voice selected:', voices[0].name);
        }
      }
    };

    if (this.synth.getVoices().length > 0) {
      setVoice();
    } else {
      this.synth.addEventListener('voiceschanged', setVoice);
      // Also try after a delay
      setTimeout(() => {
        if (this.synth && this.synth.getVoices().length > 0) {
          setVoice();
        }
      }, 1000);
    }
  }

  public speak(text: string, options: {
    rate?: number;
    pitch?: number;
    volume?: number;
    interrupt?: boolean;
  } = {}) {
    if (!this.isEnabled || !text.trim() || !this.synth) {
      return;
    }

    // Clean up text for better speech
    const cleanText = this.cleanTextForSpeech(text);

    if (options.interrupt) {
      this.stop();
    }

    const utterance = new SpeechSynthesisUtterance(cleanText);

    // Set voice if available
    if (this.voice) {
      utterance.voice = this.voice;
    }

    // Configure speech parameters for robotic voice
    utterance.rate = options.rate ?? 0.85; // Slightly slower for robotic effect
    utterance.pitch = options.pitch ?? 0.8; // Lower pitch for more masculine/robotic sound
    utterance.volume = options.volume ?? 0.7;

    // Add event listeners for debugging
    utterance.onstart = () => {
      console.log('🔊 TTS Started:', cleanText.substring(0, 50) + '...');
    };

    utterance.onerror = (event) => {
      console.error('🔊 TTS Error:', event.error);

      // Handle specific errors
      if (event.error === 'not-allowed') {
        console.warn('🔊 TTS blocked by browser. User interaction required first.');
        // Don't throw error for autoplay blocking - this is expected
      } else {
        console.error('🔊 TTS failed with error:', event.error);
      }
    };

    utterance.onend = () => {
      console.log('🔊 TTS Finished');
    };

    try {
      this.synth.speak(utterance);
    } catch (error) {
      console.error('🔊 TTS Speak failed:', error);
    }
  }

  private cleanTextForSpeech(text: string): string {
    return text
      // Remove emojis and special characters that don't speak well
      .replace(/[🤖🔋🐢💥✨🔥⚠️🎯🚀💀]/g, '')
      // Replace common game terms with more speakable versions
      .replace(/\bbatteries?\b/gi, 'battery')
      .replace(/\bturtles?\b/gi, 'turtle')
      .replace(/\bbot\b/gi, 'robot')
      // Remove excessive punctuation
      .replace(/[!]{2,}/g, '!')
      .replace(/[.]{2,}/g, '.')
      // Clean up spacing
      .replace(/\s+/g, ' ')
      .trim();
  }

  public stop() {
    if (!this.synth) return;

    try {
      this.synth.cancel();
    } catch (error) {
      console.error('🔊 TTS Stop failed:', error);
    }
  }

  public setEnabled(enabled: boolean) {
    this.isEnabled = enabled;
    if (!enabled) {
      this.stop();
    }
  }

  public isSupported(): boolean {
    return isBrowser && 'speechSynthesis' in window;
  }

  public getAvailableVoices(): SpeechSynthesisVoice[] {
    if (!this.synth) return [];
    return this.synth.getVoices();
  }

  // Test if TTS is ready (not blocked by autoplay policies)
  public async testTTS(): Promise<boolean> {
    if (!this.synth || !this.isEnabled) return false;

    return new Promise((resolve) => {
      const testUtterance = new SpeechSynthesisUtterance('');
      testUtterance.volume = 0; // Silent test

      testUtterance.onstart = () => resolve(true);
      testUtterance.onerror = (event) => {
        if (event.error === 'not-allowed') {
          resolve(false);
        } else {
          resolve(true); // Other errors don't mean TTS is blocked
        }
      };

      // Timeout fallback
      setTimeout(() => resolve(false), 1000);

      try {
        this.synth?.speak(testUtterance);
      } catch (error) {
        resolve(false);
      }
    });
  }

  // Special method for Robert's robotic voice
  public speakAsRobert(text: string, options: {
    interrupt?: boolean;
  } = {}) {
    this.speak(text, {
      rate: 0.8,        // Slower for robotic effect
      pitch: 0.7,       // Lower pitch for masculine robot voice
      volume: 0.8,      // Slightly louder for authority
      interrupt: options.interrupt
    });
  }

  // Get current voice info for debugging
  public getCurrentVoiceInfo(): string {
    if (!this.voice) return 'No voice selected';
    return `${this.voice.name} (${this.voice.lang})`;
  }

  // Get current voice for UI display
  public getCurrentVoice(): SpeechSynthesisVoice | null {
    return this.voice;
  }

  // Set voice by name
  public setVoiceByName(voiceName: string): boolean {
    if (!this.synth) return false;

    const voices = this.synth.getVoices();
    const selectedVoice = voices.find(v => v.name === voiceName);

    if (selectedVoice) {
      this.voice = selectedVoice;
      console.log('🔊 Voice changed to:', selectedVoice.name);
      return true;
    }

    return false;
  }

  // Get filtered voices for UI (English only, sorted by preference)
  public getFilteredVoices(): SpeechSynthesisVoice[] {
    if (!this.synth) return [];

    const voices = this.synth.getVoices();
    const englishVoices = voices.filter(v => v.lang.startsWith('en'));

    // Sort by preference (male voices first, then others)
    return englishVoices.sort((a, b) => {
      const aMale = this.isMaleVoice(a);
      const bMale = this.isMaleVoice(b);

      if (aMale && !bMale) return -1;
      if (!aMale && bMale) return 1;
      return a.name.localeCompare(b.name);
    });
  }

  private isMaleVoice(voice: SpeechSynthesisVoice): boolean {
    const name = voice.name.toLowerCase();
    return name.includes('male') ||
           name.includes('david') ||
           name.includes('mark') ||
           name.includes('alex') ||
           name.includes('daniel') ||
           name.includes('fred') ||
           name.includes('ralph');
  }

  // List all available voices for debugging
  public logAvailableVoices(): void {
    if (!this.synth) {
      console.log('🔊 Speech synthesis not available');
      return;
    }

    const voices = this.synth.getVoices();
    console.log('🔊 Available voices:');
    voices.forEach((voice, index) => {
      const marker = voice === this.voice ? ' ← SELECTED' : '';
      console.log(`  ${index + 1}. ${voice.name} (${voice.lang})${marker}`);
    });
  }
}

// Laser sound effect utility
export class SoundEffects {
  private static audioCache: Map<string, HTMLAudioElement> = new Map();

  public static async playLaserShot(volume: number = 0.3): Promise<void> {
    if (!isBrowser) return;

    try {
      let audio = this.audioCache.get('laser');

      if (!audio) {
        audio = new Audio('/laser-shot.mp3');
        audio.preload = 'auto';
        this.audioCache.set('laser', audio);
      }

      // Clone the audio for overlapping sounds
      const audioClone = audio.cloneNode() as HTMLAudioElement;
      audioClone.volume = volume;

      await audioClone.play();
    } catch (error) {
      console.log('🔊 Laser sound effect failed:', error);
    }
  }

  public static preloadSounds(): void {
    if (!isBrowser) return;

    // Preload laser sound
    try {
      const laserAudio = new Audio('/laser-shot.mp3');
      laserAudio.preload = 'auto';
      this.audioCache.set('laser', laserAudio);
      console.log('🔊 Sound effects preloaded');
    } catch (error) {
      console.log('🔊 Sound preload failed:', error);
    }
  }
}

// Speech-to-Text functionality using Web Speech API
export class SpeechToText {
  private static instance: SpeechToText | null = null;
  private recognition: SpeechRecognition | null = null;
  private isListening: boolean = false;
  private onResult: ((text: string) => void) | null = null;
  private onError: ((error: string) => void) | null = null;
  private onStart: (() => void) | null = null;
  private onEnd: (() => void) | null = null;

  private constructor() {
    if (isBrowser && ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window)) {
      // @ts-ignore - SpeechRecognition types may not be available
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      this.recognition = new SpeechRecognition();
      this.setupRecognition();
    }
  }

  public static getInstance(): SpeechToText {
    if (!SpeechToText.instance) {
      SpeechToText.instance = new SpeechToText();
    }
    return SpeechToText.instance;
  }

  private setupRecognition() {
    if (!this.recognition) return;

    this.recognition.continuous = false;
    this.recognition.interimResults = false;
    this.recognition.lang = 'en-US';

    this.recognition.onstart = () => {
      this.isListening = true;
      console.log('🎤 Speech recognition started');
      this.onStart?.();
    };

    this.recognition.onresult = (event) => {
      const transcript = event.results[0][0].transcript;
      console.log('🎤 Speech recognized:', transcript);
      this.onResult?.(transcript);
    };

    this.recognition.onerror = (event) => {
      console.error('🎤 Speech recognition error:', event.error);
      this.isListening = false;

      let errorMessage = 'Speech recognition failed';
      switch (event.error) {
        case 'no-speech':
          errorMessage = 'No speech detected. Please try again.';
          break;
        case 'audio-capture':
          errorMessage = 'Microphone not accessible. Please check permissions.';
          break;
        case 'not-allowed':
          errorMessage = 'Microphone permission denied. Please allow microphone access.';
          break;
        case 'network':
          errorMessage = 'Network error. Please check your connection.';
          break;
        default:
          errorMessage = `Speech recognition error: ${event.error}`;
      }

      this.onError?.(errorMessage);
    };

    this.recognition.onend = () => {
      this.isListening = false;
      console.log('🎤 Speech recognition ended');
      this.onEnd?.();
    };
  }

  public startListening(callbacks: {
    onResult?: (text: string) => void;
    onError?: (error: string) => void;
    onStart?: () => void;
    onEnd?: () => void;
  } = {}) {
    if (!this.recognition) {
      callbacks.onError?.('Speech recognition not supported in this browser');
      return;
    }

    if (this.isListening) {
      console.log('🎤 Already listening');
      return;
    }

    this.onResult = callbacks.onResult || null;
    this.onError = callbacks.onError || null;
    this.onStart = callbacks.onStart || null;
    this.onEnd = callbacks.onEnd || null;

    try {
      this.recognition.start();
    } catch (error) {
      console.error('🎤 Failed to start speech recognition:', error);
      callbacks.onError?.('Failed to start speech recognition');
    }
  }

  public stopListening() {
    if (this.recognition && this.isListening) {
      this.recognition.stop();
    }
  }

  public isSupported(): boolean {
    return isBrowser && ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window);
  }

  public getIsListening(): boolean {
    return this.isListening;
  }
}

// Export lazy singleton getters for easy use (only in browser)
export const getTts = (): TextToSpeech | null => {
  return isBrowser ? TextToSpeech.getInstance() : null;
};

export const getStt = (): SpeechToText | null => {
  return isBrowser ? SpeechToText.getInstance() : null;
};
