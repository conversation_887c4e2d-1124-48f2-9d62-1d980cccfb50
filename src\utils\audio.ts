/**
 * Audio utilities for the game
 */

// Text-to-Speech functionality using Web Speech API
export class TextToSpeech {
  private static instance: TextToSpeech;
  private synth: SpeechSynthesis;
  private isEnabled: boolean = true;
  private voice: SpeechSynthesisVoice | null = null;

  private constructor() {
    this.synth = window.speechSynthesis;
    this.initializeVoice();
  }

  public static getInstance(): TextToSpeech {
    if (!TextToSpeech.instance) {
      TextToSpeech.instance = new TextToSpeech();
    }
    return TextToSpeech.instance;
  }

  private initializeVoice() {
    // Wait for voices to be loaded
    const setVoice = () => {
      const voices = this.synth.getVoices();
      
      // Prefer English voices, with fallback options
      const preferredVoices = [
        'Google UK English Female',
        'Google US English',
        'Microsoft Zira - English (United States)',
        'Microsoft David - English (United States)',
        'Alex', // macOS
        'Samantha', // macOS
      ];

      for (const preferredName of preferredVoices) {
        const voice = voices.find(v => v.name.includes(preferredName));
        if (voice) {
          this.voice = voice;
          console.log('🔊 TTS Voice selected:', voice.name);
          return;
        }
      }

      // Fallback to any English voice
      const englishVoice = voices.find(v => v.lang.startsWith('en'));
      if (englishVoice) {
        this.voice = englishVoice;
        console.log('🔊 TTS Fallback voice selected:', englishVoice.name);
      } else if (voices.length > 0) {
        this.voice = voices[0];
        console.log('🔊 TTS Default voice selected:', voices[0].name);
      }
    };

    if (this.synth.getVoices().length > 0) {
      setVoice();
    } else {
      this.synth.addEventListener('voiceschanged', setVoice);
    }
  }

  public speak(text: string, options: {
    rate?: number;
    pitch?: number;
    volume?: number;
    interrupt?: boolean;
  } = {}) {
    if (!this.isEnabled || !text.trim()) {
      return;
    }

    // Clean up text for better speech
    const cleanText = this.cleanTextForSpeech(text);
    
    if (options.interrupt) {
      this.stop();
    }

    const utterance = new SpeechSynthesisUtterance(cleanText);
    
    // Set voice if available
    if (this.voice) {
      utterance.voice = this.voice;
    }

    // Configure speech parameters
    utterance.rate = options.rate ?? 0.9; // Slightly slower for clarity
    utterance.pitch = options.pitch ?? 1.0;
    utterance.volume = options.volume ?? 0.7;

    // Add event listeners for debugging
    utterance.onstart = () => {
      console.log('🔊 TTS Started:', cleanText.substring(0, 50) + '...');
    };

    utterance.onerror = (event) => {
      console.error('🔊 TTS Error:', event.error);
    };

    utterance.onend = () => {
      console.log('🔊 TTS Finished');
    };

    try {
      this.synth.speak(utterance);
    } catch (error) {
      console.error('🔊 TTS Speak failed:', error);
    }
  }

  private cleanTextForSpeech(text: string): string {
    return text
      // Remove emojis and special characters that don't speak well
      .replace(/[🤖🔋🐢💥✨🔥⚠️🎯🚀💀]/g, '')
      // Replace common game terms with more speakable versions
      .replace(/\bbatteries?\b/gi, 'battery')
      .replace(/\bturtles?\b/gi, 'turtle')
      .replace(/\bbot\b/gi, 'robot')
      // Remove excessive punctuation
      .replace(/[!]{2,}/g, '!')
      .replace(/[.]{2,}/g, '.')
      // Clean up spacing
      .replace(/\s+/g, ' ')
      .trim();
  }

  public stop() {
    try {
      this.synth.cancel();
    } catch (error) {
      console.error('🔊 TTS Stop failed:', error);
    }
  }

  public setEnabled(enabled: boolean) {
    this.isEnabled = enabled;
    if (!enabled) {
      this.stop();
    }
  }

  public isSupported(): boolean {
    return 'speechSynthesis' in window;
  }

  public getAvailableVoices(): SpeechSynthesisVoice[] {
    return this.synth.getVoices();
  }
}

// Laser sound effect utility
export class SoundEffects {
  private static audioCache: Map<string, HTMLAudioElement> = new Map();

  public static async playLaserShot(volume: number = 0.3): Promise<void> {
    try {
      let audio = this.audioCache.get('laser');
      
      if (!audio) {
        audio = new Audio('/laser-shot.mp3');
        audio.preload = 'auto';
        this.audioCache.set('laser', audio);
      }

      // Clone the audio for overlapping sounds
      const audioClone = audio.cloneNode() as HTMLAudioElement;
      audioClone.volume = volume;
      
      await audioClone.play();
    } catch (error) {
      console.log('🔊 Laser sound effect failed:', error);
    }
  }

  public static preloadSounds(): void {
    // Preload laser sound
    try {
      const laserAudio = new Audio('/laser-shot.mp3');
      laserAudio.preload = 'auto';
      this.audioCache.set('laser', laserAudio);
      console.log('🔊 Sound effects preloaded');
    } catch (error) {
      console.log('🔊 Sound preload failed:', error);
    }
  }
}

// Export singleton instance for easy use
export const tts = TextToSpeech.getInstance();
